/**
 * 基础编辑器样式
 * 简单、干净的样式，专注于内容编辑体验
 */

/* 编辑器容器 */
.basic-editor-container {
  position: relative;
  width: 100%;
  min-height: 60px;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex: 1; /* 占据剩余空间 */
  overflow: hidden; /* 容器本身不滚动，由内容区域处理 */
}

/* 编辑器内容区域 */
.basic-editor-content {
  width: 100%;
  height: 100%;
  overflow: auto; /* 允许内容区域滚动 */
}

/* ProseMirror 编辑器样式 */
.basic-editor-content .ProseMirror {
  outline: none;
  /* padding: 8px; */
  min-height: 60px;
  word-wrap: break-word;
  white-space: pre-wrap;
  font-family: inherit;
  font-size: inherit;
  line-height: 1.5;
}

/* 占位符样式 - 按照官方文档配置 */
.basic-editor-content .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* 空节点占位符样式 */
.basic-editor-content .ProseMirror p.is-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* 加载状态样式 */
.basic-editor-loading {
  padding: 8px;
  min-height: 60px;
  color: #adb5bd;
  display: flex;
  align-items: center;
  font-style: italic;
}

/* 基础文本样式 */
.basic-editor-content .ProseMirror h1 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.5em 0;
}

.basic-editor-content .ProseMirror h2 {
  font-size: 1.3em;
  font-weight: bold;
  margin: 0.4em 0;
}

.basic-editor-content .ProseMirror h3 {
  font-size: 1.1em;
  font-weight: bold;
  margin: 0.3em 0;
}

.basic-editor-content .ProseMirror p {
  margin: 0.5em 0;
}

.basic-editor-content .ProseMirror strong {
  font-weight: bold;
}

.basic-editor-content .ProseMirror em {
  font-style: italic;
}

.basic-editor-content .ProseMirror code {
  background-color: #f8f9fa;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.9em;
}

.basic-editor-content .ProseMirror ul,
.basic-editor-content .ProseMirror ol {
  padding-left: 1.5em;
  margin: 0.5em 0;
}

.basic-editor-content .ProseMirror li {
  margin: 0.2em 0;
}

.basic-editor-content .ProseMirror blockquote {
  border-left: 3px solid #dee2e6;
  padding-left: 1em;
  margin: 1em 0;
  font-style: italic;
  color: #6c757d;
}

.basic-editor-content .ProseMirror hr {
  border: none;
  border-top: 2px solid #dee2e6;
  margin: 1.5em 0;
}

/* 选中文本样式 */
.basic-editor-content .ProseMirror ::selection {
  background-color: #b3d4fc;
}

/* 聚焦状态 */
.basic-editor-container:focus-within {
  /* 可以添加聚焦时的样式 */
}

/* 禁用状态 */
.basic-editor-container.disabled .ProseMirror {
  color: #6c757d;
  cursor: default;
}

.basic-editor-container.disabled .ProseMirror * {
  pointer-events: none;
}

/* 表格样式 - 按照官方文档配置 */
.basic-editor-content .ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 1em 0;
  overflow: hidden;
}

.basic-editor-content .ProseMirror table td,
.basic-editor-content .ProseMirror table th {
  min-width: 1em;
  border: 1px solid #dee2e6;
  padding: 8px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.basic-editor-content .ProseMirror table th {
  font-weight: bold;
  text-align: left;
  background-color: #f8f9fa;
}

.basic-editor-content .ProseMirror table .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

.basic-editor-content .ProseMirror table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #adf;
  pointer-events: none;
}

.basic-editor-content .ProseMirror table p {
  margin: 0;
}

/* 任务列表样式 - 按照官方文档配置 */
.basic-editor-content .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding: 0;
}

.basic-editor-content .ProseMirror ul[data-type="taskList"] p {
  margin: 0;
}

.basic-editor-content .ProseMirror ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin: 0.2em 0;
}

.basic-editor-content .ProseMirror ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  user-select: none;
}

.basic-editor-content .ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

.basic-editor-content
  .ProseMirror
  ul[data-type="taskList"]
  input[type="checkbox"] {
  cursor: pointer;
}

.basic-editor-content
  .ProseMirror
  ul[data-type="taskList"]
  li[data-checked="true"]
  > div {
  text-decoration: line-through;
  color: #6c757d;
}
